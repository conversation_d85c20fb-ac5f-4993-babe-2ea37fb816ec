<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>
<div class="card-body pt-1">

    <div class="col-md-12">
        <div class="row">
            <div class="col-md-3 form-group" id="multiBlueprintSelect">
                <p class="label-text">Select Fee Type <font color="red">*</font>
                </p>
                <select class="form-control arrow-mark" id="blueprint_type" required="" name="fee_type">
                    <option value=""><?php echo 'Select Fee Type' ?></option>
                    <?php foreach ($fee_blueprints as $key => $val) { ?>
                        <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
                    <?php } ?>
                </select>
            </div>


            <div class="col-md-3 form-group">
                <p class="label-text">Class</p>
                <?php
                $array = array();
                foreach ($classes as $key => $class) {
                    $array[$class->classId] = $class->className;
                }
                echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='Select Classes' class='form-control classId select '");
                ?>
            </div>


            <div class="col-md-3 form-group">
                <p class="label-text">Select Class/Sections</p>
                <?php
                $array = array();
                // $array[0] = 'All Section';
                foreach ($classSectionList as $key => $cl) {
                    $array[$cl->id] = $cl->class_name . $cl->section_name;
                }
                echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='Select Class/Section' class='form-control select'");
                ?>
            </div>

            <!-- <div class="col-md-2 form-group" style="margin-top: 23px;">
              <input type="button" value="Get Report" id="search" class="btn btn-primary">
            </div> -->
            <div class="col-md-3 form-group d-flex justify-content-end">
                <input type="button" id="search" class="btn btn-primary" value="Get Report">
            </div>
        </div>
        <?php $this->load->helper('reports_datatable');
        echo progress_bar(); ?>
        <div class="col-md-12 pt-2" style="overflow: hidden;" id="div_id">

            <div id="fees_student_status" class="fee_balance pt-3 table-responsive">

            </div>

        </div>
    </div>


</div>
</div>

<script>
    $(document).ready(function() {
        $('#search').on('click', function() {
            var fee_type = $('#blueprint_type').val();
            if (fee_type == '') {
                return false;
            }
            $('#search').prop('disabled', true).val('Please wait...');
            // $('.loading-icon').show();
            $('.progress').show();
            $('.fee_balance').html('');
            $('.total_summary').html('');
            var classSectionId = $('#classSectionId').val();
            var classId = $('#classId').val();

            $.ajax({
                url: '<?php echo site_url('feesv2/reports_v2/get_fee_fine_waiver_student'); ?>',
                data: {
                    'fee_type': fee_type,
                    'classSectionId': classSectionId,
                    'classId': classId
                },
                type: "post",
                success: function(data) {
                    // $('.loading-icon').hide();
                    $('.progress').hide();
                    var students = JSON.parse(data);
                    // console.log(students);
                    $("#fees_student_status").html(construct_table(students));

                    const reportName = `student_fine_waiver ${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

                    $('#fine_waiver_table').DataTable({
                        ordering: false,
                        paging: true,
                        scrollY: '40vh',
                        "language": {
                            search: "",
                            searchPlaceholder: "Search",
                            lengthMenu: "Show _MENU_ ",
                            info: "Showing _START_ to _END_ of _TOTAL_ ",
                            paginate: {
                                first: "&laquo;",
                                last: "&raquo;",
                                next: "Next &rsaquo;",
                                previous: "&lsaquo; Previous"
                            }
                        },
                        "lengthMenu": [
                            [10, 25, 50, -1],
                            [10, 25, 50, "All"]
                        ],
                        "pageLength": 10,
                        columnDefs: [{
                            targets: '_all',
                            className: 'text-center'
                        }],
                        dom: '<"d-flex justify-content-between align-items-center mb-3"<"d-flex align-items-center flex-nowrap"l><"d-flex align-items-center flex-nowrap"fB>>rt<"d-flex justify-content-between align-items-center"ip>',
                        buttons: [{
                                extend: 'print',
                                text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                                filename: reportName,
                                customize: function(win) {
                                    $(win.document.body)
                                        .css('font-family', "'Poppins', sans-serif")
                                        .css('font-size', '10pt')
                                        .css('padding', '10px');

                                    $(win.document.head).append(`
                                        <style>
                                            @page {
                                            size: auto;
                                            margin: 12mm;
                                            }
                                            body {
                                            font-family: 'Poppins', sans-serif;
                                            -webkit-print-color-adjust: exact;
                                            print-color-adjust: exact;
                                            color: #333;
                                            background: #fff;
                                            }
                                            h2, h3 {
                                            text-align: center;
                                            margin-bottom: 15px;
                                            font-weight: 500;
                                            }
                                            table {
                                            border-collapse: collapse !important;
                                            width: 100% !important;
                                            margin-top: 20px;
                                            font-size: 10pt;
                                            color: #333;
                                            }
                                            th, td {
                                            border: 1px solid #ccc !important;
                                            padding: 8px 12px;
                                            text-align: left;
                                            vertical-align: middle;
                                            }
                                            th {
                                            background-color: #f4f7fc !important;
                                            font-weight: 600;
                                            color: #333;
                                            }
                                            .table-bordered {
                                            width: 100% !important;
                                            }
                                            tfoot th {
                                            background-color: #f9f9f9;
                                            font-weight: 600;
                                            }
                                        </style>
                                    `);
                                }
                            },
                            {
                                extend: 'excelHtml5',
                                text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel.svg', [], true); ?> Excel</button>`,
                                filename: reportName
                            }
                        ]
                    });
                    $('#search').prop('disabled', false).val('Get Report');
                },
                error: function(err) {
                    alert('Error');
                    console.log(err);
                    $('#search').prop('disabled', false).val('Get Report');
                }
            });
        });
    });

    function construct_table(data) {
        // console.log(data);
        var html = '';

        if (data.length == 0) {
            html += `
        <div style="font-family: 'Poppins', sans-serif; text-align: center; margin-top: 50px;">
          <h4 style="color: red; margin-top: -50px;">No Data Available</h4>
          <p style="color: black; margin: 5px 0;">There is no available data to show.</p>
          <p style="color: black;">Please choose different filters and try again.</p>
        </div>
      `;
        } else {
            html += `<table id="fine_waiver_table" class="table table-bordered">
            <thead>
              <tr>
                <th>#</th>
                <th>Created On</th>
                <th>Admission No</th>
                <th>Student Name</th>
                <th>Class/Section</th>
                <th>Remarks</th>
                <th>Amount</th>
                <th>Created By</th>
              </tr>
            </thead>`;
            html += `<tbody>`;
            for (let i = 0; i < data.length; i++) {

            }
            for (var i = 0; i < data.length; i++) {
                // console.log(data_val);
                html += `
              <tr>
                <td>${i+1}</td>
                <td>${data[i].create_date}</td>
                <td>${data[i].admission_no}</td>
                <td>${data[i].student_name}</td>
                <td>${data[i].class_section}</td>
                <td>${data[i].remarks}</td>
                <td>${data[i].amount}</td>
                <td>${data[i].created_name}</td>
                </tr>`;
            }
            html += `</tbody>
        </table>`;
        }
        return html;
    }
</script>
